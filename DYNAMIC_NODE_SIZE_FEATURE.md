# 思维导图动态节点大小功能

## 功能概述

思维导图节点现在支持根据内容动态调整大小，同时限制最大内容为 100 个字符。节点大小会根据内容自动调整，保持思维导图的整体美观性和经典的中心连接方式。

## 主要改进

### 1. 智能文本限制

- 限制节点内容最大为 100 个字符
- 实时字符计数显示，接近限制时变红提醒
- 自动截断超长文本，保持输入流畅性
- 支持多行文本显示和自动换行

### 2. 动态节点大小

- 使用 `div` + `span` 元素实现自然大小调整
- 节点宽度根据内容自动调整，最大宽度限制为 400px（根节点）/ 350px（子节点）
- 不同层级的节点有不同的最小宽度要求
- 支持 `contentEditable` 原地编辑体验

### 3. 固定距离布局（最佳实践）

- 使用固定的基础距离（200px），保持思维导图的一致性
- 连线从节点边缘连接，根据节点实际宽度计算连接点
- 采用 React Flow 等成熟项目的布局策略
- 简单可靠，避免复杂的动态计算导致的问题

## 技术实现

### 核心结构：div + span 组合

```typescript
// 节点容器使用 div，支持自然大小调整
<div
  ref={nodeRef}
  className={`mindmap-node level-${node.level}`}
  style={{
    minWidth: node.level === 0 ? "80px" : node.level === 1 ? "70px" : "60px",
    maxWidth: "500px",
    whiteSpace: "pre-wrap",
    wordWrap: "break-word",
    overflow: "visible",
    height: "auto",
    // 其他样式...
  }}
>
  {isEditing ? (
    // 编辑模式：使用 contentEditable span
    <span
      ref={editableRef}
      contentEditable
      suppressContentEditableWarning
      onInput={handleInput}
      onKeyDown={handleKeyDown}
      onBlur={onSaveEdit}
    >
      {editingText}
    </span>
  ) : (
    // 显示模式：普通 span
    <span>{node.text}</span>
  )}
</div>
```

### 关键特性

- **自然大小调整**：div 元素会根据内容自动调整大小
- **内联编辑**：contentEditable span 提供原地编辑体验
- **固定距离布局**：节点中心之间保持 200px 固定距离，简单可靠
- **边缘连线**：连线从节点边缘连接，根据节点宽度动态计算连接点
- **成熟策略**：采用 React Flow 等成熟项目验证的布局算法
- **字符限制**：100 字符限制，保持思维导图简洁性
- **实时反馈**：字符计数显示，接近限制时变红提醒

## 使用方法

1. **创建节点**：正常创建思维导图节点
2. **编辑文本**：双击节点进入编辑模式
3. **文本输入**：
   - 最多可输入 100 个字符
   - 使用 Shift+Enter 进行换行
   - 使用 Enter 保存编辑
   - 使用 Escape 取消编辑
   - 编辑时右下角显示字符计数（90+ 字符时变红提醒）
4. **自动调整**：节点会根据输入内容自动调整大小

## 特性

- ✅ 100 字符内容限制，防止节点过大
- ✅ 实时字符计数显示
- ✅ 支持单行和多行文本
- ✅ 根据内容动态调整宽度
- ✅ 保持经典的中心连接方式
- ✅ 智能最小/最大宽度限制
- ✅ 不同层级节点差异化处理
- ✅ 防重叠的智能布局算法
- ✅ 支持所有字体样式属性

## 限制

- 节点内容最大 100 个字符，确保思维导图的简洁性
- 最大宽度限制为 400px（根节点）/ 350px（子节点），避免节点过宽
- 最小宽度根据层级设置，确保视觉一致性
- 文本过长时会自动换行，超过 100 字符会被自动截断

## 兼容性

此功能与现有的所有思维导图功能完全兼容：

- 节点样式设置
- 拖拽功能
- 右键菜单
- 导入导出
- 撤销重做
