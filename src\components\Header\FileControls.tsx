import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import React from "react"
import { IconButton } from "../common/IconButton"
import {
  fileActions,
  fileInfo,
  fileOperations,
  locationOperations,
} from "./constants"
import { getIconComponent } from "./iconUtils"

export const FileControls: React.FC = () => {
  return (
    <div className="file-controls">
      {/* 基础文件操作按钮 */}
      {fileActions.slice(0, 4).map((action) => (
        <IconButton
          key={action.id}
          icon={action.icon}
          label={action.label}
          onClick={action.onClick}
          disabled={action.disabled}
        />
      ))}

      <span className="file-name">未命名文件</span>

      {/* 文件操作弹出菜单 */}
      <Popover.Root>
        <Popover.Trigger asChild>
          <IconButton
            icon={ChevronDownIcon}
            label="更多选项"
            className="file-popover-trigger"
          />
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            className="popover-content"
            side="bottom"
            align="start"
            sideOffset={4}
          >
            <div className="card-panel">
              {/* 顶部卡片 - 文件信息和操作 */}
              <div className="card-top">
                <div className="card-top-file">
                  <div className="file-icon">📄</div>
                  <div className="file-info">
                    <div className="file-name">{fileInfo.name}</div>
                    <div className="file-actions">
                      {fileOperations.map((operation, index) => (
                        <React.Fragment key={operation.id}>
                          <button
                            className="action-btn"
                            onClick={operation.onClick}
                          >
                            {React.createElement(
                              getIconComponent(operation.icon),
                              {
                                className: "action-icon",
                              }
                            )}
                            {operation.label}
                          </button>
                          {index < fileOperations.length - 1 && (
                            <div className="separator" />
                          )}
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* 底部卡片 - 位置信息和操作 */}
              <div className="card-bottom">
                <div className="location-info">
                  <div className="folder-icon">📁</div>
                  <div className="location-path">{fileInfo.location}</div>
                </div>
                <div className="location-actions">
                  {locationOperations.map((operation) => (
                    <button
                      key={operation.id}
                      className="location-btn"
                      onClick={operation.onClick}
                    >
                      {React.createElement(getIconComponent(operation.icon), {
                        className: "location-icon",
                      })}
                      {operation.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  )
}
