import React from "react"
import { FileControls } from "./FileControls"
import "./Header.css"
import { TabNavigation } from "./TabNavigation"
import { UserControls } from "./UserControls"

interface HeaderProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export const Header: React.FC<HeaderProps> = ({ activeTab, onTabChange }) => {
  return (
    <header className="header">
      <div className="header-container">
        <FileControls />
        <TabNavigation activeTab={activeTab} onTabChange={onTabChange} />
        <UserControls />
      </div>
    </header>
  )
}
