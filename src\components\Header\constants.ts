import {
  ChevronDownIcon,
  HamburgerMenuIcon,
  HomeIcon,
  PlusIcon,
  StarIcon,
} from "@radix-ui/react-icons"

// 文件操作按钮配置
export const fileActions = [
  {
    id: "home",
    icon: HomeIcon,
    label: "首页",
    onClick: () => console.log("Home clicked"),
    disabled: false,
  },
  {
    id: "add",
    icon: PlusIcon,
    label: "新建",
    onClick: () => console.log("Add clicked"),
    disabled: false,
  },
  {
    id: "menu",
    icon: HamburgerMenuIcon,
    label: "文档",
    onClick: () => console.log("Menu clicked"),
    disabled: false,
  },
  {
    id: "star",
    icon: StarIcon,
    label: "星标",
    onClick: () => console.log("Star clicked"),
    disabled: false,
  },
  {
    id: "more",
    icon: ChevronDownIcon,
    label: "更多选项",
    onClick: () => console.log("More clicked"),
    disabled: false,
  },
]

// 标签页配置
export const tabConfig = [
  { id: "开始", label: "开始" },
  { id: "样式", label: "样式" },
  { id: "插入", label: "插入" },
  { id: "视图", label: "视图" },
  { id: "导出", label: "导出" },
]

// 文件信息配置
export const fileInfo = {
  name: "阿萨德.pof",
  location: "金山办公软件有限公司 > 我的云文档",
}

// 文件操作配置
export const fileOperations = [
  {
    id: "share",
    label: "分享",
    icon: "share",
    onClick: () => console.log("Share file"),
  },
  {
    id: "star",
    label: "星标",
    icon: "star",
    onClick: () => console.log("Star file"),
  },
  {
    id: "tag",
    label: "标签",
    icon: "tag",
    onClick: () => console.log("Tag file"),
  },
]

// 位置操作配置
export const locationOperations = [
  {
    id: "move",
    label: "移动",
    icon: "move",
    onClick: () => console.log("Move file"),
  },
  {
    id: "shortcut",
    label: "添加快捷方式到",
    icon: "shortcut",
    onClick: () => console.log("Add shortcut"),
  },
] 