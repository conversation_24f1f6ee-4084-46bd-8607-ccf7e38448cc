import { useState } from 'react'
import type { ContextMenuState } from '../types/mindmap'

export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState<ContextMenuState>({
    visible: false,
    x: 0,
    y: 0,
    nodeId: null,
  })

  // 显示右键菜单
  const showContextMenu = (e: React.MouseEvent, nodeId: string) => {
    e.preventDefault()
    e.stopPropagation()
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      nodeId: nodeId,
    })
  }

  // 隐藏右键菜单
  const hideContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      nodeId: null,
    })
  }

  return {
    contextMenu,
    showContextMenu,
    hideContextMenu,
  }
}
