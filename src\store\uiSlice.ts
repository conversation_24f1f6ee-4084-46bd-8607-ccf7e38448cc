import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { UIState } from '../types'

const initialState: UIState = {
  activeTab: "开始",
  showAddButton: null
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload
    },

    setShowAddButton: (state, action: PayloadAction<string | null>) => {
      state.showAddButton = action.payload
    }
  }
})

export const {
  setActiveTab,
  setShowAddButton
} = uiSlice.actions

export default uiSlice.reducer
