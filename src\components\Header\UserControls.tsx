import { PersonIcon } from "@radix-ui/react-icons"
import React from "react"

export const UserControls: React.FC = () => {
  const handleShare = () => {
    console.log("Share clicked")
  }

  return (
    <div className="user-controls">
      <button className="share-btn" onClick={handleShare} aria-label="分享">
        分享
      </button>
      <button className="user-avatar" aria-label="用户">
        <PersonIcon />
      </button>
    </div>
  )
}
