import React from "react"
import { tabConfig } from "./constants"

interface TabNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
}) => {
  return (
    <nav className="tab-navigation">
      {tabConfig.map((tab: { id: string; label: string }) => (
        <button
          key={tab.id}
          className={`tab-btn ${activeTab === tab.id ? "active" : ""}`}
          onClick={() => onTabChange(tab.id)}
          aria-label={tab.label}
        >
          {tab.label}
        </button>
      ))}
    </nav>
  )
}
