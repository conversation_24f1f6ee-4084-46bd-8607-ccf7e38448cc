import React from "react"

// 分享图标
export const ShareIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    style={{ transform: "scale(0.8)" }}
  >
    <path
      d="M697.355812 24.532387c20.897959-29.984028 63.602484-31.801242 93.586513-8.177462l231.694765 188.990239v48.156167L789.125111 447.034605c-27.258208 19.080745-78.140195 15.446318-91.769299-6.360248v-89.043479c-169.909494 1.817214-329.824312 131.748004-437.948536 328.915706V520.631766c52.699201-228.968944 198.984916-377.980479 437.948536-424.319432V24.532387z"
      fill="#707070"
    />
    <path
      d="M1.36291 646.019521c0-31.801242 25.440994-56.333629 56.333629-56.333629s56.333629 24.532387 56.33363 56.333629v195.350488c1.817214 19.080745 8.177462 34.527063 19.080745 46.338953s27.258208 19.989352 48.156167 23.62378h597.863354c21.806566-3.634428 37.252884-11.81189 48.156167-23.62378 11.81189-11.81189 18.172138-27.258208 19.080745-46.338953V646.019521c0-31.801242 25.440994-56.333629 56.333629-56.333629s56.333629 24.532387 56.333629 56.333629v198.076309c-1.817214 47.24756-19.080745 89.952085-49.973381 121.753327-29.075421 29.984028-69.054126 50.881988-119.936113 57.242236l-4.543035 0.908607H176.724046l-5.451641-0.908607c-49.973381-6.360248-90.860692-27.258208-119.027507-57.242236-31.801242-31.801242-48.156167-74.505768-50.881988-121.753327V646.019521z"
      fill="#707070"
    />
  </svg>
)

// 星标图标
export const StarIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    style={{ transform: "scale(0.9)" }}
  >
    <path
      d="M761.2 942.8c-8.9 0-17.9-1.8-26.5-5.4l-220.7-93-220.7 93c-22.1 9.3-46.8 6.4-66.2-7.6-19.4-14.1-29.8-36.7-27.7-60.6l20.2-238.7L62.9 449.2c-15.7-18.1-20.6-42.5-13.2-65.3 7.4-22.8 25.7-39.6 49.1-45.1L332 284.3l123.9-205c12.4-20.5 34.1-32.7 58-32.7 24 0 45.7 12.2 58.1 32.7l123.9 205 233.2 54.5c23.3 5.5 41.7 22.3 49.1 45.1 7.4 22.8 2.5 47.2-13.2 65.3L808.4 630.4l20.2 238.7c2 23.9-8.3 46.5-27.7 60.6-11.8 8.6-25.7 13-39.7 13.1zM146.4 415.3L289 580.2c11.9 13.7 17.8 32 16.3 50.1l-18.4 217.2 200.8-84.6c16.8-7 35.9-7 52.6 0l200.9 84.6-18.4-217.2c-1.5-18.1 4.4-36.4 16.3-50.1l142.5-164.9-212.2-49.6c-17.7-4.1-33.2-15.4-42.6-31L514 148.3 401.2 334.8c-9.4 15.6-24.9 26.8-42.6 31l-212.2 49.5z m553.3-124.7h0.2-0.2z"
      fill="#707070"
    />
  </svg>
)

// 标签图标
export const TagIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    style={{ transform: "scale(1.1)" }}
  >
    <path
      d="M448 853.333333L170.666667 576 576 170.666667H853.333333v277.333333L448 853.333333z m0-119.466666L768 426.666667V256h-170.666667l-307.2 320 157.866667 157.866667zM682.666667 298.666667c25.6 0 42.666667 17.066667 42.666666 42.666666s-17.066667 42.666667-42.666666 42.666667-42.666667-17.066667-42.666667-42.666667 17.066667-42.666667 42.666667-42.666666z"
      fill="#707070"
    />
  </svg>
)

// 移动图标
export const MoveIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg className={className} viewBox="0 0 1024 1024" width="12" height="12">
    <path
      d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
      fill="#6b7280"
    />
    <path
      d="M464 688a48 48 0 1 0 96 0 48 48 0 1 0-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"
      fill="#6b7280"
    />
  </svg>
)

// 快捷方式图标
export const ShortcutIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg className={className} viewBox="0 0 1024 1024" width="12" height="12">
    <path
      d="M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zM368 744c0 4.4-3.6 8-8 8h-56c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v464zm152-280c0 4.4-3.6 8-8 8h-56c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v184zm152 72c0 4.4-3.6 8-8 8h-56c-4.4 0-8-3.6-8-8V280c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v256z"
      fill="#6b7280"
    />
  </svg>
)
