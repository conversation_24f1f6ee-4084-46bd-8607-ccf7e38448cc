import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import type { ToolbarSectionProps } from "../types"
import { ToolbarButton, ToolbarSelect } from "../ui"

export const StyleToolbar: React.FC<ToolbarSectionProps> = ({
  selectedNodeId,
  selectedNodeStyle,
  onBorderWidthChange,
}) => {
  const isEnabled = selectedNodeId !== null

  return (
    <ToolbarPrimitive.Root className="toolbar" aria-label="样式工具栏">
      {/* 节点样式 */}
      <ToolbarButton label="节点样式" disabled={!isEnabled}>
        节点样式
      </ToolbarButton>

      {/* 形状 */}
      <ToolbarButton label="形状" disabled={!isEnabled}>
        形状
      </ToolbarButton>

      {/* 节点颜色 */}
      <ToolbarButton label="节点颜色" disabled={!isEnabled}>
        节点颜色
      </ToolbarButton>

      {/* 连接线颜色 */}
      <ToolbarButton label="连接线颜色" disabled={!isEnabled}>
        连接线颜色
      </ToolbarButton>

      {/* 连接线类型 */}
      <ToolbarButton label="连接线类型" disabled={!isEnabled}>
        连接线类型
      </ToolbarButton>

      {/* 边框颜色 */}
      <ToolbarButton label="边框颜色" disabled={!isEnabled}>
        边框颜色
      </ToolbarButton>

      {/* 边框宽度组 */}
      <div className="toolbar-border-width-group">
        <ToolbarButton label="边框宽度" disabled={!isEnabled}>
          边框宽度
        </ToolbarButton>
        <ToolbarSelect
          label="选择边框宽度"
          options={["0", "1", "2", "3", "4", "5"]}
          value={(selectedNodeStyle?.borderWidth?.toString() as string) || "1"}
          onChange={(val) =>
            onBorderWidthChange && onBorderWidthChange(parseInt(val, 10))
          }
          disabled={!isEnabled}
          className="toolbar-border-width-select"
        />
      </div>

      {/* 边框类型 */}
      <ToolbarButton label="边框类型" disabled={!isEnabled}>
        边框类型
      </ToolbarButton>

      {/* 清除样式 */}
      <ToolbarButton label="清除样式" disabled={!isEnabled}>
        清除样式
      </ToolbarButton>
    </ToolbarPrimitive.Root>
  )
}
