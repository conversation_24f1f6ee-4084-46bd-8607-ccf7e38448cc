import type { MindMapNode, NodeStyle } from './mindmap'

// Redux状态类型定义
export interface MindMapState {
  nodes: Record<string, MindMapNode>
  selectedNodeId: string | null
}

export interface UIState {
  activeTab: string
  showAddButton: string | null
}

export interface EditorState {
  isEditingNode: string | null
  editingText: string
  showStylePanel: string | null
  editingStyle: NodeStyle
}

// 根状态类型
export interface RootState {
  mindMap: MindMapState
  ui: UIState
  editor: EditorState
}
