# 新增功能说明

## 工具栏增强功能

### 1. Hover 提示功能

为工具栏的所有按钮添加了固定的 `title` 属性，鼠标悬停时显示功能提示：

- **加粗按钮**: "加粗 (Ctrl+B)"
- **斜体按钮**: "斜体 (Ctrl+I)"
- **下划线按钮**: "下划线 (Ctrl+U)"
- **字体颜色**: "字体颜色"
- **对齐按钮**: "左对齐"、"居中对齐"、"右对齐"
- **其他按钮**: 包含相应的功能说明和快捷键

### 2. 文本格式化功能

#### 加粗功能

- **位置**: 工具栏第一个按钮组
- **图标**: FontBoldIcon
- **功能**: 切换选中节点文本的加粗状态
- **状态显示**: 当节点文本为加粗时，按钮显示激活状态
- **快捷键提示**: Ctrl+B

#### 斜体功能

- **位置**: 工具栏第一个按钮组
- **图标**: FontItalicIcon
- **功能**: 切换选中节点文本的斜体状态
- **状态显示**: 当节点文本为斜体时，按钮显示激活状态
- **快捷键提示**: Ctrl+I

#### 字体颜色选择器

- **位置**: 工具栏第一个按钮组
- **组件**: 自定义 ColorPicker 组件
- **样式**: 标签样式 (A + 下划线颜色条 + 下拉箭头)
- **功能**: 选择节点文本颜色
- **特性**:
  - 简洁的标签样式按钮
  - 点击后弹出专业的 SketchPicker 颜色选择器
  - 预定义颜色调色板（80 种常用颜色）
  - HSV/RGB/Hex 颜色模式切换
  - 颜色滑块和色相环
  - 实时颜色预览和应用

## 技术实现

### 新增组件

#### ColorPicker 组件

- **文件**: `src/components/ColorPicker.tsx`
- **依赖**: `@radix-ui/react-popover`, `react-color`
- **功能**:
  - 标签样式的颜色选择按钮 (A + 颜色下划线 + 下拉箭头)
  - 点击后弹出专业的 SketchPicker 颜色选择器
  - 预定义颜色调色板（80 种常用颜色）
  - HSV/RGB/Hex 颜色模式切换
  - 颜色滑块和色相环
  - 专业级颜色选择体验

### 新增 Hook 功能

#### useMindMap Hook 增强

- 添加了节点样式更新功能
- 支持实时样式变更

#### 新增处理函数

- `handleToggleBold()`: 切换加粗状态
- `handleToggleItalic()`: 切换斜体状态
- `handleColorChange()`: 处理颜色变更

### 组件更新

#### Toolbar 组件

- **新增 Props**:
  - `selectedNodeStyle?: NodeStyle` - 当前选中节点的样式
  - `onToggleBold?: () => void` - 加粗切换回调
  - `onToggleItalic?: () => void` - 斜体切换回调
  - `onColorChange?: (color: string) => void` - 颜色变更回调
- 拆分

1. **图标组件化** ：将所有 SVG 图标提取为独立的 React 组件
2. **UI 组件分离** ：基础 UI 组件（按钮、选择器等）独立管理
3. **功能分组** ：按功能将工具栏分为开始、样式、导出三个部分
4. **类型集中管理** ：所有 TypeScript 接口统一定义
5. **清晰的导入导出** ：使用 index.ts 文件统一管理导出

#### MindMapPage 组件

- 集成新的样式处理逻辑
- 传递节点样式状态给工具栏

## 使用方法

1. **选择节点**: 点击任意思维导图节点
2. **加粗文本**: 点击工具栏的加粗按钮 (B)
3. **斜体文本**: 点击工具栏的斜体按钮 (I)
4. **更改颜色**: 点击颜色选择器，从调色板选择或输入自定义颜色
5. **查看提示**: 将鼠标悬停在任意工具栏按钮上查看功能说明

## 样式状态

- 工具栏按钮会根据当前选中节点的样式状态显示激活状态
- 颜色选择器显示当前节点的文字颜色
- 所有样式变更会实时应用到选中的节点
- 样式变更会自动保存到本地存储

## 兼容性

- 完全兼容现有的思维导图功能
- 不影响节点的创建、删除、编辑等操作
- 样式信息会随思维导图一起导入导出
