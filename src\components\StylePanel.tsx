/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  FontBoldIcon,
  FontItalicIcon,
  UnderlineIcon,
} from "@radix-ui/react-icons"
import type { NodeStyle } from "../types/mindmap"
import { ColorPicker } from "./ColorPicker"

interface StylePanelProps {
  nodeId: string
  nodeX: number
  nodeY: number
  style: NodeStyle
  onStyleChange: (style: NodeStyle) => void
}

export const StylePanel = ({
  nodeId: _nodeId,
  nodeX,
  nodeY,
  style,
  onStyleChange,
}: StylePanelProps) => {
  const updateStyle = (updates: Partial<NodeStyle>) => {
    const newStyle = { ...style, ...updates }
    onStyleChange(newStyle)
  }

  return (
    <div
      className="style-panel"
      style={{
        left: nodeX - 100,
        top: nodeY + 40,
        zIndex: 1001,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      <div className="style-panel-content">
        <div className="style-toolbar">
          <button
            className={`style-btn ${
              style.fontWeight === "bold" ? "active" : ""
            }`}
            onClick={() => {
              const newWeight: "normal" | "bold" =
                style.fontWeight === "bold" ? "normal" : "bold"
              console.log("点击粗体按钮，新权重:", newWeight)
              updateStyle({ fontWeight: newWeight })
            }}
            title="粗体"
          >
            <FontBoldIcon />
          </button>
          <button
            className={`style-btn ${
              style.fontStyle === "italic" ? "active" : ""
            }`}
            onClick={() => {
              updateStyle({
                fontStyle: style.fontStyle === "italic" ? "normal" : "italic",
              })
            }}
            title="斜体"
          >
            <FontItalicIcon />
          </button>
          <button
            className={`style-btn ${
              style.textDecoration === "underline" ? "active" : ""
            }`}
            onClick={() => {
              updateStyle({
                textDecoration:
                  style.textDecoration === "underline" ? "none" : "underline",
              })
            }}
            title="下划线"
          >
            <UnderlineIcon />
          </button>

          {/* 分隔线 */}
          <div
            style={{
              width: "1px",
              height: "20px",
              backgroundColor: "#e5e7eb",
              margin: "0 8px",
            }}
          ></div>

          {/* 字体大小 */}
          <input
            type="number"
            min="10"
            max="24"
            value={style.fontSize}
            onChange={(e) => {
              updateStyle({ fontSize: parseInt(e.target.value) || 14 })
            }}
            className="style-input"
            style={{
              width: "50px",
              height: "26px",
              fontSize: "12px",
            }}
            title="字体大小"
          />

          {/* 分隔线 */}
          <div
            style={{
              width: "1px",
              height: "20px",
              backgroundColor: "#e5e7eb",
              margin: "0 8px",
            }}
          ></div>

          {/* 文字颜色 */}
          <ColorPicker
            value={style.color}
            onChange={(color: string) => updateStyle({ color })}
            title="文字颜色"
            icon="A"
          />

          {/* 背景颜色 */}
          <ColorPicker
            value={style.backgroundColor}
            onChange={(color: string) =>
              updateStyle({ backgroundColor: color })
            }
            title="背景颜色"
            icon="■"
          />
        </div>
      </div>
    </div>
  )
}
