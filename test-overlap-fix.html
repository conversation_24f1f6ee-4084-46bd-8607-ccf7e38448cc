<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>节点重叠修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-steps {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .step {
            margin-bottom: 5px;
        }
        .expected {
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #28a745;
            margin-top: 10px;
        }
        .note {
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ffc107;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>思维导图节点重叠修复测试指南</h1>
        <p>以下是验证节点重叠修复效果的测试用例。请按照步骤进行测试，确认修复是否有效。</p>

        <div class="test-case">
            <div class="test-title">测试用例 1: 根节点多个子节点重叠</div>
            <div class="test-description">
                测试当根节点有多个子节点时，特别是包含长文本的节点，是否会发生重叠。
            </div>
            <div class="test-steps">
                <div class="step">1. 打开思维导图应用</div>
                <div class="step">2. 在根节点添加 5-6 个子节点</div>
                <div class="step">3. 将其中 2-3 个节点的文本修改为长文本（如图片中的长串 "dddddd..."）</div>
                <div class="step">4. 观察节点是否重叠</div>
            </div>
            <div class="expected">
                <strong>预期结果：</strong> 所有节点应该有足够的垂直间距，不会重叠覆盖。长文本节点应该有更大的间距。
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 2: 分支节点的子节点重叠</div>
            <div class="test-description">
                测试分支节点（非根节点）的子节点是否会重叠。
            </div>
            <div class="test-steps">
                <div class="step">1. 选择一个分支节点</div>
                <div class="step">2. 为该分支节点添加多个子节点（4-5个）</div>
                <div class="step">3. 将部分子节点设置为长文本</div>
                <div class="step">4. 观察子节点的排列</div>
            </div>
            <div class="expected">
                <strong>预期结果：</strong> 分支节点的子节点应该垂直排列，有足够间距，不会重叠。
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 3: 混合文本长度节点</div>
            <div class="test-description">
                测试包含不同长度文本的节点混合排列时的效果。
            </div>
            <div class="test-steps">
                <div class="step">1. 创建多个子节点，包含：</div>
                <div class="step">   - 短文本节点（如 "子主题"）</div>
                <div class="step">   - 中等长度文本节点</div>
                <div class="step">   - 长文本节点（多行或很长的单行）</div>
                <div class="step">2. 观察它们的间距分配</div>
            </div>
            <div class="expected">
                <strong>预期结果：</strong> 间距应该基于最高节点动态调整，确保所有节点都有足够空间。
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">测试用例 4: 动态添加节点</div>
            <div class="test-description">
                测试动态添加节点时布局是否正确重新计算。
            </div>
            <div class="test-steps">
                <div class="step">1. 从一个简单的思维导图开始</div>
                <div class="step">2. 逐步添加新的子节点</div>
                <div class="step">3. 在添加过程中修改某些节点为长文本</div>
                <div class="step">4. 观察每次添加后的布局调整</div>
            </div>
            <div class="expected">
                <strong>预期结果：</strong> 每次添加节点后，布局应该自动重新计算，避免重叠。
            </div>
        </div>

        <div class="note">
            <strong>注意：</strong>
            <ul>
                <li>如果仍然出现重叠，请检查浏览器控制台是否有错误信息</li>
                <li>测试时请确保浏览器窗口足够大，以便观察完整的布局</li>
                <li>可以尝试刷新页面重新加载，确认修复是否持久有效</li>
            </ul>
        </div>

        <div class="test-case">
            <div class="test-title">🚀 全新区域布局算法</div>
            <div class="test-description">
                完全重写了布局算法，实现基于区域的思维导图布局：
            </div>
            <div class="test-steps">
                <div class="step">🎯 <strong>区域概念</strong>：每个分支节点及其所有后代节点形成一个独立区域</div>
                <div class="step">📏 <strong>子树边界计算</strong>：准确计算每个子树的实际占用空间</div>
                <div class="step">🔄 <strong>两阶段布局</strong>：先递归计算子树，再调整整体位置</div>
                <div class="step">📐 <strong>动态间距</strong>：根据子树实际高度分配垂直空间</div>
                <div class="step">🎨 <strong>区域隔离</strong>：确保不同分支的区域永不重叠</div>
                <div class="step">⚡ <strong>智能调整</strong>：整个子树作为单位进行位置调整</div>
            </div>
            <div class="expected">
                <strong>算法优势：</strong>
                <ul>
                    <li>✅ 完全消除节点重叠问题</li>
                    <li>✅ 支持任意深度的子树</li>
                    <li>✅ 自动适应不同大小的节点</li>
                    <li>✅ 保持思维导图的视觉平衡</li>
                    <li>✅ 高效的计算性能</li>
                </ul>
            </div>
        </div>

        <div class="test-case">
            <div class="test-title">🔧 算法工作原理</div>
            <div class="test-description">
                新算法的核心工作流程：
            </div>
            <div class="test-steps">
                <div class="step"><strong>步骤 1：</strong> 设置所有子节点的水平位置（X坐标）</div>
                <div class="step"><strong>步骤 2：</strong> 递归计算每个子节点的完整子树</div>
                <div class="step"><strong>步骤 3：</strong> 计算每个子树的实际边界框（minY, maxY, height）</div>
                <div class="step"><strong>步骤 4：</strong> 根据子树高度重新分配垂直空间</div>
                <div class="step"><strong>步骤 5：</strong> 整体调整每个子树的位置，确保区域不重叠</div>
            </div>
            <div class="note">
                <strong>关键创新：</strong> 算法将每个分支视为一个整体区域，而不是单独处理每个节点，从根本上解决了重叠问题。
            </div>
        </div>
    </div>
</body>
</html>
