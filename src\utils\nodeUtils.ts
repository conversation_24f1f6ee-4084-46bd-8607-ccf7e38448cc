import type { MindMapNode, NodeStyle } from '../types/mindmap'

// 默认节点样式
export const getDefaultNodeStyle = (): NodeStyle => ({
  fontFamily: "微软雅黑",
  fontSize: 14,
  fontWeight: "normal",
  fontStyle: "normal",
  textDecoration: "none",
  color: "#000000",
  backgroundColor: "#ffffff",
  borderColor: "#d1d5db",
  borderWidth: 1,
})

// 获取节点默认名称
export const getDefaultNodeName = (parentLevel: number) => {
  switch (parentLevel) {
    case 0: // 根节点的子节点
      return "分支主题"
    case 1: // 分支主题的子节点
      return "子主题"
    case 2: // 子主题的子节点
      return "子主题"
    default:
      return "新节点"
  }
}

// 估算节点宽度（更精确的版本）
const estimateNodeWidth = (node: MindMapNode): number => {
  // 尝试从DOM获取实际宽度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetWidth > 0) {
    return nodeElement.offsetWidth
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 处理多行文本，计算最宽的一行
  const lines = text.split('\n')
  let maxLineLength = 0
  lines.forEach(line => {
    maxLineLength = Math.max(maxLineLength, line.length)
  })

  // 更精确的字符宽度计算
  const charWidth = fontSize * 0.65 // 稍微增加字符宽度估算
  const textWidth = maxLineLength * charWidth

  // 加上padding和边框
  const padding = node.level === 0 ? 40 : node.level === 1 ? 32 : 24
  const estimatedWidth = textWidth + padding

  // 设置最小和最大宽度，与CSS保持一致
  const minWidth = node.level === 0 ? 120 : node.level === 1 ? 100 : 80
  const maxWidth = node.level === 0 ? 800 : 600 // 与CSS中的maxWidth保持一致

  return Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
}

// 估算节点高度（新增函数）
const estimateNodeHeight = (node: MindMapNode): number => {
  // 尝试从DOM获取实际高度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetHeight > 0) {
    return nodeElement.offsetHeight
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 计算行数
  const lines = text.split('\n')
  const lineCount = lines.length

  // 行高约为字体大小的1.2倍
  const lineHeight = fontSize * 1.2
  const textHeight = lineCount * lineHeight

  // 加上padding
  const padding = node.level === 0 ? 24 : node.level === 1 ? 20 : 16
  const estimatedHeight = textHeight + padding

  // 设置最小高度
  const minHeight = node.level === 0 ? 40 : node.level === 1 ? 36 : 32

  return Math.max(estimatedHeight, minHeight)
}

// 检查两个节点是否重叠
const checkNodeOverlap = (node1: MindMapNode, node2: MindMapNode, margin: number = 20): boolean => {
  const width1 = estimateNodeWidth(node1)
  const height1 = estimateNodeHeight(node1)
  const width2 = estimateNodeWidth(node2)
  const height2 = estimateNodeHeight(node2)

  // 计算节点的边界（考虑transform: translate(-50%, -50%)）
  const left1 = node1.x - width1 / 2 - margin
  const right1 = node1.x + width1 / 2 + margin
  const top1 = node1.y - height1 / 2 - margin
  const bottom1 = node1.y + height1 / 2 + margin

  const left2 = node2.x - width2 / 2 - margin
  const right2 = node2.x + width2 / 2 + margin
  const top2 = node2.y - height2 / 2 - margin
  const bottom2 = node2.y + height2 / 2 + margin

  // 检查是否重叠
  return !(right1 < left2 || left1 > right2 || bottom1 < top2 || top1 > bottom2)
}

// 计算动态垂直间距
const calculateDynamicSpacing = (nodes: MindMapNode[], baseSpacing: number = 80): number => {
  if (nodes.length === 0) return baseSpacing

  // 计算所有节点的最大高度，而不是平均高度
  const maxHeight = Math.max(...nodes.map(node => estimateNodeHeight(node)))

  // 基于最大高度动态调整间距，确保即使是最高的节点也有足够的空间
  const dynamicSpacing = Math.max(baseSpacing, maxHeight + 60)

  // 对于长文本节点，进一步增加间距
  const hasLongText = nodes.some(node => {
    const text = node.text || ""
    return text.length > 50 || text.split('\n').length > 2
  })

  if (hasLongText) {
    return Math.max(dynamicSpacing, maxHeight + 100)
  }

  return dynamicSpacing
}

// 重新计算所有节点位置 - 改进的思维导图布局（避免重叠）
export const recalculateAllPositions = (mindMapNodes: Record<string, MindMapNode>) => {
  const updatedNodes = { ...mindMapNodes }

  // 从根节点开始重新计算所有位置
  const calculateLevel = (parentId: string, level: number) => {
    const parent = updatedNodes[parentId]
    if (!parent || parent.children.length === 0) return

    const childCount = parent.children.length
    const children = parent.children.map(id => updatedNodes[id]).filter(Boolean)

    // 动态计算垂直间距，基于子节点的实际高度
    const dynamicSpacing = calculateDynamicSpacing(children)

    // 计算父节点的实际宽度
    const parentWidth = estimateNodeWidth(parent)

    // 动态计算基础距离，考虑节点宽度
    const baseDistance = Math.max(200, parentWidth / 2 + 100)

    if (parent.id === "root") {
      // 根节点的子节点：左右分布
      const rightSideCount = Math.ceil(childCount / 2)
      const leftSideCount = Math.floor(childCount / 2)

      // 分别处理左右两侧
      const rightSideChildren = children.slice(0, rightSideCount)
      const leftSideChildren = children.slice(rightSideCount)

      // 处理右侧节点
      const rightSpacing = calculateDynamicSpacing(rightSideChildren)
      const rightTotalHeight = (rightSideCount - 1) * rightSpacing
      const rightStartY = parent.y - rightTotalHeight / 2

      rightSideChildren.forEach((child, index) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 50)

        let newX = parent.x + distance
        let newY = rightStartY + index * rightSpacing

        // 检查并避免与其他节点重叠
        newY = avoidOverlap(updatedNodes, child.id, newX, newY, level + 1)

        updatedNodes[child.id] = {
          ...child,
          x: newX,
          y: newY,
        }
      })

      // 处理左侧节点
      if (leftSideChildren.length > 0) {
        const leftSpacing = calculateDynamicSpacing(leftSideChildren)
        const leftTotalHeight = (leftSideCount - 1) * leftSpacing
        const leftStartY = parent.y - leftTotalHeight / 2

        leftSideChildren.forEach((child, index) => {
          const childWidth = estimateNodeWidth(child)
          const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 50)

          let newX = parent.x - distance
          let newY = leftStartY + index * leftSpacing

          // 检查并避免与其他节点重叠
          newY = avoidOverlap(updatedNodes, child.id, newX, newY, level + 1)

          updatedNodes[child.id] = {
            ...child,
            x: newX,
            y: newY,
          }
        })
      }
    } else {
      // 分支节点的子节点：继承父节点的方向
      const isParentOnRight = parent.x > updatedNodes["root"].x

      // 计算初始位置
      const totalHeight = (childCount - 1) * dynamicSpacing
      const startY = parent.y - totalHeight / 2

      children.forEach((child, index) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 50)

        let newX = isParentOnRight
          ? parent.x + distance
          : parent.x - distance
        let newY = startY + index * dynamicSpacing

        // 检查并避免与其他节点重叠
        newY = avoidOverlap(updatedNodes, child.id, newX, newY, level)

        updatedNodes[child.id] = {
          ...child,
          x: newX,
          y: newY,
        }
      })
    }

    // 递归计算下一级
    parent.children.forEach(childId => {
      calculateLevel(childId, level + 1)
    })
  }

  calculateLevel("root", 0)
  return updatedNodes
}

// 避免节点重叠的辅助函数
const avoidOverlap = (
  nodes: Record<string, MindMapNode>,
  currentNodeId: string,
  x: number,
  y: number,
  level: number
): number => {
  const currentNode = { ...nodes[currentNodeId], x, y }
  const currentHeight = estimateNodeHeight(currentNode)

  // 获取所有可能重叠的节点（已经定位的节点）
  const otherNodes = Object.values(nodes).filter(node =>
    node.id !== currentNodeId &&
    node.x !== undefined && node.y !== undefined && // 已经定位的节点
    Math.abs(node.x - x) < 500 // 扩大检查范围
  )

  let adjustedY = y
  let attempts = 0
  const maxAttempts = 30
  const minSpacing = 20 // 最小间距

  while (attempts < maxAttempts) {
    const testNode = { ...currentNode, y: adjustedY }
    let hasOverlap = false
    let conflictNode = null

    for (const otherNode of otherNodes) {
      if (checkNodeOverlap(testNode, otherNode, minSpacing)) {
        hasOverlap = true
        conflictNode = otherNode
        break
      }
    }

    if (!hasOverlap) {
      return adjustedY
    }

    // 如果有重叠，计算需要移动的距离
    if (conflictNode) {
      const conflictHeight = estimateNodeHeight(conflictNode)
      const requiredSpacing = (currentHeight + conflictHeight) / 2 + minSpacing

      // 根据冲突节点的位置决定移动方向
      if (adjustedY < conflictNode.y) {
        // 当前节点在冲突节点上方，向上移动
        adjustedY = conflictNode.y - requiredSpacing
      } else {
        // 当前节点在冲突节点下方，向下移动
        adjustedY = conflictNode.y + requiredSpacing
      }
    } else {
      // 如果没有找到具体的冲突节点，使用原来的逻辑
      const stepSize = currentHeight + minSpacing
      if (attempts % 2 === 0) {
        adjustedY = y + Math.ceil(attempts / 2) * stepSize
      } else {
        adjustedY = y - Math.ceil(attempts / 2) * stepSize
      }
    }

    attempts++
  }

  return adjustedY
}

// 导出思维导图为JSON文件
export const exportMindMap = (mindMapNodes: Record<string, MindMapNode>) => {
  try {
    const dataStr = JSON.stringify(mindMapNodes, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `mindmap-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出思维导图失败:", error)
    alert("导出失败，请重试")
  }
}
