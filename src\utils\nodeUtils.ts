import type { MindMapNode, NodeStyle } from '../types/mindmap'

// 默认节点样式
export const getDefaultNodeStyle = (): NodeStyle => ({
  fontFamily: "微软雅黑",
  fontSize: 14,
  fontWeight: "normal",
  fontStyle: "normal",
  textDecoration: "none",
  color: "#000000",
  backgroundColor: "#ffffff",
  borderColor: "#d1d5db",
  borderWidth: 1,
})

// 获取节点默认名称
export const getDefaultNodeName = (parentLevel: number) => {
  switch (parentLevel) {
    case 0: // 根节点的子节点
      return "分支主题"
    case 1: // 分支主题的子节点
      return "子主题"
    case 2: // 子主题的子节点
      return "子主题"
    default:
      return "新节点"
  }
}

// 估算节点宽度（更精确的版本）
const estimateNodeWidth = (node: MindMapNode): number => {
  // 尝试从DOM获取实际宽度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetWidth > 0) {
    return nodeElement.offsetWidth
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 处理多行文本，计算最宽的一行
  const lines = text.split('\n')
  let maxLineLength = 0
  lines.forEach(line => {
    maxLineLength = Math.max(maxLineLength, line.length)
  })

  // 更精确的字符宽度计算
  const charWidth = fontSize * 0.65 // 稍微增加字符宽度估算
  const textWidth = maxLineLength * charWidth

  // 加上padding和边框
  const padding = node.level === 0 ? 40 : node.level === 1 ? 32 : 24
  const estimatedWidth = textWidth + padding

  // 设置最小和最大宽度，与CSS保持一致
  const minWidth = node.level === 0 ? 120 : node.level === 1 ? 100 : 80
  const maxWidth = node.level === 0 ? 800 : 600 // 与CSS中的maxWidth保持一致

  return Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
}

// 重新计算所有节点位置
export const recalculateAllPositions = (mindMapNodes: Record<string, MindMapNode>) => {
  const updatedNodes = { ...mindMapNodes }

  // 从根节点开始重新计算所有位置
  const calculateLevel = (parentId: string, level: number) => {
    const parent = updatedNodes[parentId]
    if (!parent || parent.children.length === 0) return

    const childCount = parent.children.length
    const verticalSpacing = 120 // 子节点之间的垂直间距

    // 计算起始Y位置，使子节点垂直居中
    const totalHeight = (childCount - 1) * verticalSpacing
    const startY = parent.y - totalHeight / 2

    parent.children.forEach((childId, index) => {
      if (updatedNodes[childId]) {
        let newX: number
        let newY: number

        if (parent.id === "root") {
          // 根节点的子节点：左右分布
          if (index < Math.ceil(childCount / 2)) {
            // 前一半放右边
            newX = parent.x + 200
          } else {
            // 后一半放左边
            newX = parent.x - 200
          }

          // 计算在各自一侧的索引
          const sideIndex =
            index < Math.ceil(childCount / 2)
              ? index
              : index - Math.ceil(childCount / 2)
          const sideCount =
            index < Math.ceil(childCount / 2)
              ? Math.ceil(childCount / 2)
              : Math.floor(childCount / 2)
          const sideHeight = (sideCount - 1) * verticalSpacing
          const sideStartY = parent.y - sideHeight / 2
          newY = sideStartY + sideIndex * verticalSpacing
        } else {
          // 分支节点的子节点：继承父节点的方向
          const isParentOnRight = parent.x > updatedNodes["root"].x
          newX = isParentOnRight ? parent.x + 200 : parent.x - 200
          newY = startY + index * verticalSpacing
        }

        updatedNodes[childId] = {
          ...updatedNodes[childId],
          x: newX,
          y: newY,
        }

        // 递归计算下一级
        calculateLevel(childId, level + 1)
      }
    })
  }

  calculateLevel("root", 0)
  return updatedNodes
}

// 导出思维导图为JSON文件
export const exportMindMap = (mindMapNodes: Record<string, MindMapNode>) => {
  try {
    const dataStr = JSON.stringify(mindMapNodes, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `mindmap-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出思维导图失败:", error)
    alert("导出失败，请重试")
  }
}
