import type { MindMapNode, NodeStyle } from '../types/mindmap'

// 默认节点样式
export const getDefaultNodeStyle = (): NodeStyle => ({
  fontFamily: "微软雅黑",
  fontSize: 14,
  fontWeight: "normal",
  fontStyle: "normal",
  textDecoration: "none",
  color: "#000000",
  backgroundColor: "#ffffff",
  borderColor: "#d1d5db",
  borderWidth: 1,
})

// 获取节点默认名称
export const getDefaultNodeName = (parentLevel: number) => {
  switch (parentLevel) {
    case 0: // 根节点的子节点
      return "分支主题"
    case 1: // 分支主题的子节点
      return "子主题"
    case 2: // 子主题的子节点
      return "子主题"
    default:
      return "新节点"
  }
}

// 估算节点宽度（更精确的版本）
const estimateNodeWidth = (node: MindMapNode): number => {
  // 尝试从DOM获取实际宽度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetWidth > 0) {
    return nodeElement.offsetWidth
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 处理多行文本，计算最宽的一行
  const lines = text.split('\n')
  let maxLineLength = 0
  lines.forEach(line => {
    maxLineLength = Math.max(maxLineLength, line.length)
  })

  // 更精确的字符宽度计算
  const charWidth = fontSize * 0.65 // 稍微增加字符宽度估算
  const textWidth = maxLineLength * charWidth

  // 加上padding和边框
  const padding = node.level === 0 ? 40 : node.level === 1 ? 32 : 24
  const estimatedWidth = textWidth + padding

  // 设置最小和最大宽度，与CSS保持一致
  const minWidth = node.level === 0 ? 120 : node.level === 1 ? 100 : 80
  const maxWidth = node.level === 0 ? 800 : 600 // 与CSS中的maxWidth保持一致

  return Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
}

// 估算节点高度（新增函数）
const estimateNodeHeight = (node: MindMapNode): number => {
  // 尝试从DOM获取实际高度
  const nodeElement = document.querySelector(`[data-node-id="${node.id}"]`) as HTMLElement
  if (nodeElement && nodeElement.offsetHeight > 0) {
    return nodeElement.offsetHeight
  }

  const fontSize = node.style?.fontSize || 14
  const text = node.text || "新节点"

  // 计算行数
  const lines = text.split('\n')
  const lineCount = lines.length

  // 行高约为字体大小的1.2倍
  const lineHeight = fontSize * 1.2
  const textHeight = lineCount * lineHeight

  // 加上padding
  const padding = node.level === 0 ? 24 : node.level === 1 ? 20 : 16
  const estimatedHeight = textHeight + padding

  // 设置最小高度
  const minHeight = node.level === 0 ? 40 : node.level === 1 ? 36 : 32

  return Math.max(estimatedHeight, minHeight)
}



// 计算动态垂直间距
const calculateDynamicSpacing = (nodes: MindMapNode[], baseSpacing: number = 80): number => {
  if (nodes.length === 0) return baseSpacing

  // 计算所有节点的最大高度，而不是平均高度
  const maxHeight = Math.max(...nodes.map(node => estimateNodeHeight(node)))

  // 基于最大高度动态调整间距，确保即使是最高的节点也有足够的空间
  const dynamicSpacing = Math.max(baseSpacing, maxHeight + 60)

  // 对于长文本节点，进一步增加间距
  const hasLongText = nodes.some(node => {
    const text = node.text || ""
    return text.length > 50 || text.split('\n').length > 2
  })

  if (hasLongText) {
    return Math.max(dynamicSpacing, maxHeight + 100)
  }

  return dynamicSpacing
}

// 计算子树的边界框
const calculateSubtreeBounds = (nodeId: string, nodes: Record<string, MindMapNode>): {
  minY: number
  maxY: number
  height: number
} => {
  const node = nodes[nodeId]
  if (!node) return { minY: 0, maxY: 0, height: 0 }

  const nodeHeight = estimateNodeHeight(node)
  let minY = node.y - nodeHeight / 2
  let maxY = node.y + nodeHeight / 2

  // 递归计算所有子节点的边界
  if (node.children && node.children.length > 0) {
    for (const childId of node.children) {
      const childBounds = calculateSubtreeBounds(childId, nodes)
      minY = Math.min(minY, childBounds.minY)
      maxY = Math.max(maxY, childBounds.maxY)
    }
  }

  return {
    minY,
    maxY,
    height: maxY - minY
  }
}

// 调整子树位置的辅助函数
const adjustSubtreePosition = (nodeId: string, nodes: Record<string, MindMapNode>, deltaY: number) => {
  const node = nodes[nodeId]
  if (!node) return

  // 调整当前节点位置
  nodes[nodeId] = {
    ...node,
    y: node.y + deltaY
  }

  // 递归调整所有子节点
  if (node.children && node.children.length > 0) {
    node.children.forEach(childId => {
      adjustSubtreePosition(childId, nodes, deltaY)
    })
  }
}

// 重新计算所有节点位置 - 基于区域的布局算法
export const recalculateAllPositions = (mindMapNodes: Record<string, MindMapNode>) => {
  const updatedNodes = { ...mindMapNodes }

  // 从根节点开始重新计算所有位置
  const calculateLevel = (parentId: string, level: number) => {
    const parent = updatedNodes[parentId]
    if (!parent || parent.children.length === 0) return

    const childCount = parent.children.length
    const children = parent.children.map(id => updatedNodes[id]).filter(Boolean)

    // 计算父节点的实际宽度
    const parentWidth = estimateNodeWidth(parent)
    const baseDistance = Math.max(200, parentWidth / 2 + 100)

    if (parent.id === "root") {
      // 根节点的子节点：左右分布，使用区域布局
      const rightSideCount = Math.ceil(childCount / 2)
      const leftSideCount = Math.floor(childCount / 2)

      const rightSideChildren = children.slice(0, rightSideCount)
      const leftSideChildren = children.slice(rightSideCount)

      // 先设置所有子节点的X位置并递归计算子树
      children.forEach((child, index) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 50)

        if (index < rightSideCount) {
          updatedNodes[child.id] = {
            ...child,
            x: parent.x + distance,
            y: parent.y, // 临时Y位置
          }
        } else {
          updatedNodes[child.id] = {
            ...child,
            x: parent.x - distance,
            y: parent.y, // 临时Y位置
          }
        }

        // 递归计算子节点
        calculateLevel(child.id, level + 1)
      })

      // 计算每个子树的高度，并重新分配Y位置
      const rightSubtreeHeights = rightSideChildren.map(child =>
        calculateSubtreeBounds(child.id, updatedNodes).height
      )
      const leftSubtreeHeights = leftSideChildren.map(child =>
        calculateSubtreeBounds(child.id, updatedNodes).height
      )

      // 为右侧子树分配位置
      if (rightSideChildren.length > 0) {
        const rightTotalHeight = rightSubtreeHeights.reduce((sum, h) => sum + h, 0) +
                                (rightSideChildren.length - 1) * 60 // 子树间距
        let rightCurrentY = parent.y - rightTotalHeight / 2

        rightSideChildren.forEach((child, index) => {
          const subtreeHeight = rightSubtreeHeights[index]
          const centerY = rightCurrentY + subtreeHeight / 2

          // 调整整个子树的位置
          adjustSubtreePosition(child.id, updatedNodes, centerY - child.y)
          rightCurrentY += subtreeHeight + 60 // 子树间距
        })
      }

      // 为左侧子树分配位置
      if (leftSideChildren.length > 0) {
        const leftTotalHeight = leftSubtreeHeights.reduce((sum, h) => sum + h, 0) +
                               (leftSideChildren.length - 1) * 60 // 子树间距
        let leftCurrentY = parent.y - leftTotalHeight / 2

        leftSideChildren.forEach((child, index) => {
          const subtreeHeight = leftSubtreeHeights[index]
          const centerY = leftCurrentY + subtreeHeight / 2

          // 调整整个子树的位置
          adjustSubtreePosition(child.id, updatedNodes, centerY - child.y)
          leftCurrentY += subtreeHeight + 60 // 子树间距
        })
      }
    } else {
      // 分支节点的子节点：垂直排列，使用区域布局
      const isParentOnRight = parent.x > updatedNodes["root"].x

      // 先设置子节点的X位置并递归计算
      children.forEach((child) => {
        const childWidth = estimateNodeWidth(child)
        const distance = Math.max(baseDistance, parentWidth / 2 + childWidth / 2 + 50)

        updatedNodes[child.id] = {
          ...child,
          x: isParentOnRight ? parent.x + distance : parent.x - distance,
          y: parent.y, // 临时Y位置
        }

        // 递归计算子节点
        calculateLevel(child.id, level + 1)
      })

      // 计算每个子树的高度并重新分配Y位置
      const subtreeHeights = children.map(child =>
        calculateSubtreeBounds(child.id, updatedNodes).height
      )

      const totalHeight = subtreeHeights.reduce((sum, h) => sum + h, 0) +
                         (children.length - 1) * 60 // 子树间距
      let currentY = parent.y - totalHeight / 2

      children.forEach((child, index) => {
        const subtreeHeight = subtreeHeights[index]
        const centerY = currentY + subtreeHeight / 2

        // 调整整个子树的位置
        adjustSubtreePosition(child.id, updatedNodes, centerY - child.y)
        currentY += subtreeHeight + 60 // 子树间距
      })
    }
  }

  calculateLevel("root", 0)
  return updatedNodes
}



// 导出思维导图为JSON文件
export const exportMindMap = (mindMapNodes: Record<string, MindMapNode>) => {
  try {
    const dataStr = JSON.stringify(mindMapNodes, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = `mindmap-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error("导出思维导图失败:", error)
    alert("导出失败，请重试")
  }
}
