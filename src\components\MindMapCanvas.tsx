import React, { useEffect, useState } from "react"
import type {
    MindMapNode as MindMapNodeType,
    NodeStyle,
    ViewOffset,
} from "../types/mindmap"
import { MindMapNode } from "./MindMapNode"

interface MindMapCanvasProps {
  mindMapNodes: Record<string, MindMapNodeType>
  selectedNodeId: string | null
  isEditingNode: string | null
  editingText: string
  editingStyle: NodeStyle
  showStylePanel: string | null
  showAddButton: string | null
  isDragging: boolean
  viewOffset: ViewOffset
  onNodeSelect: (nodeId: string) => void
  onNodeDoubleClick: (nodeId: string) => void
  onNodeContextMenu: (e: React.MouseEvent, nodeId: string) => void
  onNodeMouseDown: (e: React.MouseEvent) => void
  onEditingTextChange: (text: string) => void
  onSaveEdit: () => void
  onCancelEdit: () => void
  onStyleChange: (nodeId: string, style: NodeStyle) => void
  onAddChildNode: (nodeId: string) => void
  onCanvasClick: () => void
  onMouseMove: (e: React.MouseEvent) => void
  onMouseUp: () => void
  onMouseLeave: () => void
  onToggleBold?: () => void
  onToggleItalic?: () => void
  onToggleUnderline?: () => void
  onColorChange?: (color: string) => void
  onFontSizeChange?: (fontSize: number) => void
  onTextAlignChange?: (align: "left" | "center" | "right") => void
}

export const MindMapCanvas = ({
  mindMapNodes,
  selectedNodeId,
  isEditingNode,
  editingText,
  editingStyle,
  showStylePanel,
  showAddButton,
  isDragging,
  viewOffset,
  onNodeSelect,
  onNodeDoubleClick,
  onNodeContextMenu,
  onNodeMouseDown,
  onEditingTextChange,
  onSaveEdit,
  onCancelEdit,
  onStyleChange,
  onAddChildNode,
  onCanvasClick,
  onMouseMove,
  onMouseUp,
  onMouseLeave,
  onToggleBold,
  onToggleItalic,
  onToggleUnderline,
  onColorChange,
  onFontSizeChange,
  onTextAlignChange,
}: MindMapCanvasProps) => {
  // 添加一个状态来强制重新渲染
  const [renderKey, setRenderKey] = useState(0)

  // 监听布局重新计算事件
  useEffect(() => {
    const handleRecalculateLayout = () => {
      // 强制重新渲染以获取最新的DOM尺寸
      setTimeout(() => {
        setRenderKey((prev) => prev + 1)
      }, 100)
    }

    window.addEventListener("recalculateLayout", handleRecalculateLayout)
    return () => {
      window.removeEventListener("recalculateLayout", handleRecalculateLayout)
    }
  }, [])

  return (
    <div
      className="flex-1 mindmap-container"
      onClick={(e) => {
        // 只在点击空白区域时触发
        if (e.target === e.currentTarget) {
          onCanvasClick()
        }
      }}
      onMouseMove={onMouseMove}
      onMouseUp={onMouseUp}
      onMouseLeave={onMouseLeave}
    >
      {/* SVG for drawing lines between nodes */}
      <svg key={renderKey} className="mindmap-svg">


        {/* 渲染节点间的连接线 */}
        {Object.values(mindMapNodes).map((node) => {
          if (!node.parentId) return null

          const parent = mindMapNodes[node.parentId]

          // 获取节点实际宽度和高度的函数
          const getActualNodeDimensions = (targetNode: MindMapNodeType) => {
            // 尝试从DOM获取实际尺寸
            const nodeElement = document.querySelector(
              `[data-node-id="${targetNode.id}"]`
            ) as HTMLElement
            if (nodeElement && nodeElement.offsetWidth > 0) {
              return {
                width: nodeElement.offsetWidth,
                height: nodeElement.offsetHeight,
              }
            }

            // 如果无法获取DOM元素，使用更精确的估算
            const fontSize = targetNode.style?.fontSize || 14
            const text = targetNode.text || "新节点"

            // 处理多行文本，计算最宽的一行和行数
            const lines = text.split("\n")
            let maxLineLength = 0
            lines.forEach((line) => {
              maxLineLength = Math.max(maxLineLength, line.length)
            })

            // 更精确的字符宽度计算
            const charWidth = fontSize * 0.65
            const textWidth = maxLineLength * charWidth
            const padding =
              targetNode.level === 0 ? 40 : targetNode.level === 1 ? 32 : 24
            const estimatedWidth = textWidth + padding
            const minWidth =
              targetNode.level === 0 ? 120 : targetNode.level === 1 ? 100 : 80
            const maxWidth = targetNode.level === 0 ? 600 : 500

            const width = Math.min(Math.max(estimatedWidth, minWidth), maxWidth)
            const lineHeight = fontSize * 1.4
            const height = lines.length * lineHeight + padding * 0.8

            return { width, height }
          }

          const parentDimensions = getActualNodeDimensions(parent)
          const nodeDimensions = getActualNodeDimensions(node)
          const parentWidth = parentDimensions.width
          const nodeWidth = nodeDimensions.width

          // 获取父节点的所有子节点
          const siblings = Object.values(mindMapNodes).filter(
            (n) => n.parentId === parent.id
          )

          // 根据子节点数量和位置决定连线方式
          const sameSideSiblings = siblings.filter(
            (sibling) => sibling.x > parent.x === node.x > parent.x
          )
          const sameSideIndex = sameSideSiblings.findIndex(
            (n) => n.id === node.id
          )
          const sameSideCount = sameSideSiblings.length

          // 计算连线的起点和终点
          let x1, x2, y1, y2
          let useDirectLine = false

          if (sameSideCount <= 3) {
            // 1-3个节点：使用原有的分散连接逻辑
            if (sameSideCount === 1) {
              // 只有一个节点：直线从中心连接
              useDirectLine = true
              y1 = parent.y + viewOffset.y
            } else if (sameSideCount === 2) {
              // 两个节点：都用弧线，分散连接点
              const parentFontSize = parent.style?.fontSize || 14
              const parentLines = (parent.text || "新节点").split("\n").length
              const parentHeight = Math.max(
                parentFontSize * parentLines + 20,
                40
              )

              const connectionSpacing = parentHeight / 3
              y1 =
                parent.y +
                viewOffset.y +
                (sameSideIndex === 0 ? -connectionSpacing : connectionSpacing)
            } else if (sameSideCount === 3) {
              // 三个节点：中间的直线，上下的弧线
              if (sameSideIndex === 1) {
                // 中间的节点用直线
                useDirectLine = true
                y1 = parent.y + viewOffset.y
              } else {
                // 上下的节点用弧线
                const parentFontSize = parent.style?.fontSize || 14
                const parentLines = (parent.text || "新节点").split("\n").length
                const parentHeight = Math.max(
                  parentFontSize * parentLines + 20,
                  40
                )

                y1 =
                  parent.y +
                  viewOffset.y +
                  (sameSideIndex === 0 ? -parentHeight / 2 : parentHeight / 2)
              }
            }
          } else {
            // 超过3个节点：统一从左上角和右下角连出
            const parentFontSize = parent.style?.fontSize || 14
            const parentLines = (parent.text || "新节点").split("\n").length
            const parentHeight = Math.max(parentFontSize * parentLines + 20, 40)

            // 判断节点在父节点的哪一侧
            const isOnRightSide = node.x > parent.x

            if (isOnRightSide) {
              // 右侧节点：从右下角连出
              y1 = parent.y + viewOffset.y + parentHeight / 3
            } else {
              // 左侧节点：从左上角连出
              y1 = parent.y + viewOffset.y - parentHeight / 3
            }
          }

          y2 = node.y + viewOffset.y

          // 计算X坐标，考虑是否使用角落连接点
          if (sameSideCount > 3) {
            // 超过3个节点：使用角落连接点
            if (node.x > parent.x) {
              // 右侧节点：从父节点右下角连出
              x1 = parent.x + parentWidth / 3 + viewOffset.x
              x2 = node.x - nodeWidth / 2 + viewOffset.x
            } else {
              // 左侧节点：从父节点左上角连出
              x1 = parent.x - parentWidth / 3 + viewOffset.x
              x2 = node.x + nodeWidth / 2 + viewOffset.x
            }
          } else {
            // 3个或更少节点：使用中心连接点
            if (node.x > parent.x) {
              x1 = parent.x + parentWidth / 2 + viewOffset.x
              x2 = node.x - nodeWidth / 2 + viewOffset.x
            } else {
              x1 = parent.x - parentWidth / 2 + viewOffset.x
              x2 = node.x + nodeWidth / 2 + viewOffset.x
            }
          }

          // 根据连线类型创建路径
          let pathData: string
          if (useDirectLine) {
            // 直线连接
            pathData = `M ${x1} ${y1} L ${x2} ${y2}`
          } else {
            // 弧线连接
            const dx = x2 - x1
            const controlDistance = Math.abs(dx) * 0.3
            const controlX1 = x1 + (dx > 0 ? controlDistance : -controlDistance)
            const controlY1 = y1
            const controlX2 = x2 - (dx > 0 ? controlDistance : -controlDistance)
            const controlY2 = y2

            pathData = `M ${x1} ${y1} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${x2} ${y2}`
          }

          return (
            <path
              key={`line-${node.id}`}
              className="mindmap-line"
              d={pathData}
              fill="none"
            />
          )
        })}
      </svg>

      {/* Mind Map Nodes Container */}
      <div
        className="mindmap-nodes-container"
        style={{
          transform: `translate(${viewOffset.x}px, ${viewOffset.y}px)`,
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
        }}
      >
        {Object.values(mindMapNodes).map((node) => (
          <MindMapNode
            key={node.id}
            node={node}
            rootNode={mindMapNodes.root}
            isSelected={selectedNodeId === node.id}
            isEditing={isEditingNode === node.id}
            editingText={editingText}
            editingStyle={editingStyle}
            showStylePanel={showStylePanel === node.id}
            showAddButton={showAddButton === node.id}
            isDragging={isDragging}
            onSelect={() => onNodeSelect(node.id)} // 单击选中节点（用于header工具栏）
            onDoubleClick={() => onNodeDoubleClick(node.id)} // 双击进入编辑模式（显示节点工具栏）
            onContextMenu={(e) => onNodeContextMenu(e, node.id)}
            onMouseDown={onNodeMouseDown}
            onEditingTextChange={onEditingTextChange}
            onSaveEdit={onSaveEdit}
            onCancelEdit={onCancelEdit}
            onStyleChange={(style) => onStyleChange(node.id, style)}
            onAddChild={() => {
              onAddChildNode(node.id)
            }}
            onToggleBold={onToggleBold}
            onToggleItalic={onToggleItalic}
            onToggleUnderline={onToggleUnderline}
            onColorChange={onColorChange}
            onFontSizeChange={onFontSizeChange}
            onTextAlignChange={onTextAlignChange}
          />
        ))}
      </div>
    </div>
  )
}
