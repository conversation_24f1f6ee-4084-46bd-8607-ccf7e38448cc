import * as Tooltip from "@radix-ui/react-tooltip"
import React from "react"
import { ExportToolbar, StartToolbar, StyleToolbar } from "./sections"
import type { ToolbarProps } from "./types"

export const Toolbar: React.FC<ToolbarProps> = (props) => {
  const {
    activeTab,
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
  } = props

  const sectionProps = {
    selectedNodeId,
    selectedNodeStyle,
    onAddChildNode,
    onToggleBold,
    onToggleItalic,
    onColorChange,
    onFontFamilyChange,
    onFontSizeChange,
    onTextAlignChange,
    onBorderWidthChange,
  }

  const renderToolbar = () => {
    switch (activeTab) {
      case "开始":
        return <StartToolbar {...sectionProps} />
      case "样式":
        return <StyleToolbar {...sectionProps} />
      case "导出":
        return <ExportToolbar />
      case "插入":
        return <StartToolbar {...sectionProps} />
      case "视图":
        return <div className="toolbar"></div>
      default:
        return <div className="toolbar"></div>
    }
  }

  return (
    <Tooltip.Provider delayDuration={100}>{renderToolbar()}</Tooltip.Provider>
  )
}
