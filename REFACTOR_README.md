# 思维导图

## 项目结构

重构后的项目采用了更清晰的目录结构，将原本集中在 `App.tsx` 中的所有代码按功能进行了拆分：

```
src/
├── components/          # 可复用组件
│   ├── Header.tsx       # 顶部导航栏
│   ├── Toolbar.tsx      # 工具栏
│   ├── MindMapCanvas.tsx # 思维导图画布
│   ├── MindMapNode.tsx  # 思维导图节点
│   ├── StylePanel.tsx   # 样式面板
│   └── ContextMenu.tsx  # 右键菜单
├── hooks/               # 自定义 Hooks
│   ├── useMindMap.ts    # 思维导图核心逻辑
│   ├── useNodeEditor.ts # 节点编辑逻辑
│   ├── useViewDrag.ts   # 视图拖拽逻辑
│   └── useContextMenu.ts # 右键菜单逻辑
├── pages/               # 页面组件
│   └── MindMapPage.tsx  # 思维导图主页面
├── types/               # 类型定义
│   └── mindmap.ts       # 思维导图相关类型
├── utils/               # 工具函数
│   ├── storage.ts       # 本地存储工具
│   └── nodeUtils.ts     # 节点操作工具
├── App.tsx              # 应用入口（简化）
└── main.tsx             # React 入口
```

## 重构亮点

### 1. 组件化设计

- **Header**: 独立的顶部导航栏组件，包含文件控制、标签导航和用户控制
- **Toolbar**: 可配置的工具栏组件，根据当前标签显示不同工具
- **MindMapCanvas**: 思维导图画布，负责渲染连接线和节点容器
- **MindMapNode**: 单个思维导图节点组件，支持编辑和样式设置
- **StylePanel**: 样式编辑面板，提供字体、颜色等样式选项
- **ContextMenu**: 右键菜单组件，提供节点操作选项

### 2. 自定义 Hooks

- **useMindMap**: 核心思维导图逻辑，包含节点增删改查、位置计算等
- **useNodeEditor**: 节点编辑状态管理，包含编辑模式切换和样式编辑
- **useViewDrag**: 视图拖拽功能，支持画布平移
- **useContextMenu**: 右键菜单状态管理

### 3. 类型安全

- **NodeStyle**: 节点样式类型定义
- **MindMapNode**: 思维导图节点类型定义
- **ContextMenuState**: 右键菜单状态类型
- **DragOffset/ViewOffset**: 拖拽和视图偏移类型

### 4. 工具函数分离

- **storage.ts**: localStorage 操作封装
- **nodeUtils.ts**: 节点操作工具函数，包含位置计算、导出等功能

## 技术栈

- **React 19**: 最新版本的 React
- **TypeScript**: 类型安全的 JavaScript
- **Radix UI**: 高质量的 UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Vite**: 快速的构建工具

## 优势

1. **可维护性**: 代码按功能模块化，易于维护和扩展
2. **可复用性**: 组件和 Hooks 可以在其他项目中复用
3. **类型安全**: 完整的 TypeScript 类型定义
4. **性能优化**: 合理的组件拆分避免不必要的重渲染
5. **开发体验**: 清晰的项目结构提升开发效率

## 运行项目

```bash
cd homework-3/client
npm install
npm run dev
```

访问 http://localhost:5173 查看应用。

## 功能特性

- ✅ 思维导图节点的增删改查
- ✅ 节点样式自定义（字体、颜色、大小等）
- ✅ 拖拽视图平移
- ✅ 右键菜单操作
- ✅ 本地存储自动保存
- ✅ 导入/导出 JSON 文件
- ✅ 键盘快捷键支持
- ✅ 响应式设计

## 后续优化建议

1. 添加单元测试
2. 实现撤销/重做功能
3. 支持更多导出格式（PNG、PDF等）
4. 添加主题切换功能
5. 实现协作编辑功能
