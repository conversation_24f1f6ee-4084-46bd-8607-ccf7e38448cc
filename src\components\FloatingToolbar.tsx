import { ChevronDownIcon } from "@radix-ui/react-icons"
import * as Popover from "@radix-ui/react-popover"
import * as Select from "@radix-ui/react-select"
import * as Toolbar from "@radix-ui/react-toolbar"
import * as Tooltip from "@radix-ui/react-tooltip"
import { ColorPicker } from "./ColorPicker"

interface ToolbarButtonItem {
  type: "button"
  label: string
  icon: React.ReactNode
  onClick: () => void
  isActive?: boolean
}

interface ToolbarSelectItem {
  type: "select"
  options: string[]
  value: string
  onChange: (value: string) => void
  width?: string
}

interface ToolbarColorItem {
  type: "color"
  value: string
  onChange: (color: string) => void
}

interface ToolbarSeparatorItem {
  type: "separator"
}

type ToolbarConfig = (
  | ToolbarButtonItem
  | ToolbarSelectItem
  | ToolbarColorItem
  | ToolbarSeparatorItem
)[]

interface FloatingToolbarProps {
  position: { x: number; y: number }
  config: ToolbarConfig
}

export const FloatingToolbar = ({ position, config }: FloatingToolbarProps) => {
  return (
    <Toolbar.Root
      className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-md flex flex-wrap items-center gap-2 p-2 max-w-[90vw]"
      style={{ left: position.x, top: position.y }}
      onClick={(e) => e.stopPropagation()}
    >
      {config.map((item, index) => {
        switch (item.type) {
          case "button":
            return (
              <Tooltip.Provider key={index}>
                <Tooltip.Root>
                  <Tooltip.Trigger asChild>
                    <Toolbar.Button
                      onClick={(e) => {
                        e.stopPropagation()
                        item.onClick()
                      }}
                      data-state={item.isActive ? "on" : "off"}
                      className="p-2 border border-gray-300 rounded hover:bg-gray-100 data-[state=on]:bg-blue-100"
                    >
                      {item.icon}
                    </Toolbar.Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content className="bg-black text-white text-xs px-2 py-1 rounded">
                    {item.label}
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            )

          case "select":
            return (
              <Select.Root
                key={index}
                value={item.value}
                onValueChange={item.onChange}
              >
                <Select.Trigger
                  className={`flex items-center justify-between ${
                    item.width || "w-[80px]"
                  } px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50`}
                >
                  <Select.Value />
                  <Select.Icon>
                    <ChevronDownIcon />
                  </Select.Icon>
                </Select.Trigger>
                <Select.Content className="bg-white border border-gray-200 rounded shadow-md">
                  <Select.Viewport>
                    {item.options.map((opt) => (
                      <Select.Item
                        key={opt}
                        value={opt}
                        className="px-3 py-1 text-sm cursor-pointer hover:bg-gray-100"
                      >
                        {opt}
                      </Select.Item>
                    ))}
                  </Select.Viewport>
                </Select.Content>
              </Select.Root>
            )

          case "color":
            return (
              <Popover.Root key={index}>
                <Popover.Trigger asChild>
                  <Toolbar.Button className="p-2 border border-gray-300 rounded hover:bg-gray-100">
                    🎨
                  </Toolbar.Button>
                </Popover.Trigger>
                <Popover.Content className="bg-white border border-gray-200 rounded p-2 shadow-md">
                  <ColorPicker value={item.value} onChange={item.onChange} />
                </Popover.Content>
              </Popover.Root>
            )

          case "separator":
            return (
              <div
                key={index}
                className="w-px h-5 bg-gray-300 mx-1"
                aria-hidden="true"
              />
            )

          default:
            return null
        }
      })}
    </Toolbar.Root>
  )
}
