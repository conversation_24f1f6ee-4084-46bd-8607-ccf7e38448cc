// 节点样式类型
export interface NodeStyle {
  fontSize: number
  fontFamily: string
  fontWeight: "normal" | "bold"
  fontStyle: "normal" | "italic"
  textDecoration: "none" | "underline"
  color: string
  backgroundColor: string
  borderColor: string
  borderWidth: number
  textAlign?: "left" | "center" | "right"
}

// 思维导图节点类型
export interface MindMapNode {
  id: string
  text: string
  x: number
  y: number
  level: number
  parentId?: string
  children: string[]
  style?: NodeStyle
}

// 右键菜单状态类型
export interface ContextMenuState {
  visible: boolean
  x: number
  y: number
  nodeId: string | null
}

// 拖拽偏移类型
export interface DragOffset {
  x: number
  y: number
}

// 视图偏移类型
export interface ViewOffset {
  x: number
  y: number
}
