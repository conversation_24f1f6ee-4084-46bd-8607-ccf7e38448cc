import { PlusIcon } from "@radix-ui/react-icons"
import type { ContextMenuState, MindMapNode } from "../types/mindmap"

interface ContextMenuProps {
  contextMenu: ContextMenuState
  mindMapNodes: Record<string, MindMapNode>
  onAddChildNode: (nodeId: string) => void
  onAddSiblingNode: (nodeId: string) => void
  onAddParentNode: (nodeId: string) => void
  onDeleteNode: (nodeId: string) => void
  onHide: () => void
}

export const ContextMenu = ({
  contextMenu,
  mindMapNodes,
  onAddChildNode,
  onAddSiblingNode,
  onAddParentNode,
  onDeleteNode,
  onHide,
}: ContextMenuProps) => {
  if (!contextMenu.visible) return null

  // 计算菜单位置，确保在页面内完整显示
  const calculateMenuPosition = () => {
    const MENU_WIDTH = 220
    const MENU_HEIGHT = 500 // 估算菜单高度
    const PADDING = 10 // 距离边缘的最小距离

    const screenWidth = window.innerWidth
    const screenHeight = window.innerHeight

    let left = contextMenu.x
    let top = contextMenu.y

    // 如果菜单会超出右边界，则向左调整
    if (left + MENU_WIDTH + PADDING > screenWidth) {
      left = screenWidth - MENU_WIDTH - PADDING
    }

    // 如果菜单会超出下边界，则向上调整
    if (top + MENU_HEIGHT + PADDING > screenHeight) {
      top = screenHeight - MENU_HEIGHT - PADDING
    }

    // 确保不会超出左边界和上边界
    left = Math.max(PADDING, left)
    top = Math.max(PADDING, top)

    return { left, top }
  }

  const menuPosition = calculateMenuPosition()

  return (
    <>
      <div className="context-menu-overlay" onClick={onHide} />
      <div
        className="context-menu"
        style={{
          left: menuPosition.left,
          top: menuPosition.top,
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <button
          className={`context-menu-item ${
            contextMenu.nodeId && mindMapNodes[contextMenu.nodeId]?.level >= 2
              ? "disabled"
              : ""
          }`}
          onClick={() => {
            if (contextMenu.nodeId) {
              onAddChildNode(contextMenu.nodeId)
              onHide()
            }
          }}
          disabled={
            contextMenu.nodeId
              ? mindMapNodes[contextMenu.nodeId]?.level >= 2
              : true
          }
        >
          <span className="flex items-center">
            <PlusIcon className="icon" />
            新增子主题
          </span>
          <span className="shortcut">Tab</span>
        </button>

        {/* 新增同级主题 - 只对分支节点启用 */}
        <button
          className={`context-menu-item ${
            !contextMenu.nodeId || contextMenu.nodeId === "root"
              ? "disabled"
              : ""
          }`}
          onClick={() => {
            if (contextMenu.nodeId && contextMenu.nodeId !== "root") {
              onAddSiblingNode(contextMenu.nodeId)
              onHide()
            }
          }}
          disabled={!contextMenu.nodeId || contextMenu.nodeId === "root"}
        >
          <span className="flex items-center">
            <PlusIcon className="icon" />
            新增同级主题
          </span>
          <span className="shortcut">Enter</span>
        </button>

        {/* 新增父主题 - 只对分支节点启用 */}
        <button
          className={`context-menu-item ${
            !contextMenu.nodeId || contextMenu.nodeId === "root"
              ? "disabled"
              : ""
          }`}
          onClick={() => {
            if (contextMenu.nodeId && contextMenu.nodeId !== "root") {
              onAddParentNode(contextMenu.nodeId)
              onHide()
            }
          }}
          disabled={!contextMenu.nodeId || contextMenu.nodeId === "root"}
        >
          <span className="flex items-center">
            <PlusIcon className="icon" />
            新增父主题
          </span>
          <span className="shortcut">Shift + Tab</span>
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          插入
        </button>

        <button className="context-menu-item disabled" disabled>
          编号
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          收起主题
        </button>

        <button className="context-menu-item disabled" disabled>
          选择主题
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          <span>复制主题</span>
          <span className="shortcut">Ctrl + C</span>
        </button>

        <button className="context-menu-item disabled" disabled>
          <span>剪切</span>
          <span className="shortcut">Ctrl + X</span>
        </button>

        <button className="context-menu-item disabled" disabled>
          <span>粘贴主题</span>
          <span className="shortcut">Ctrl + V</span>
        </button>

        <div className="context-menu-separator"></div>

        <button
          className={`context-menu-item ${
            contextMenu.nodeId === "root" ? "disabled" : ""
          }`}
          onClick={() => {
            if (contextMenu.nodeId && contextMenu.nodeId !== "root") {
              onDeleteNode(contextMenu.nodeId)
            }
          }}
          disabled={contextMenu.nodeId === "root"}
        >
          <span>删除</span>
          <span className="shortcut">Del</span>
        </button>

        <button className="context-menu-item disabled" disabled>
          <span>删除当前主题</span>
          <span className="shortcut">Ctrl + Del</span>
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          批量删除
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          <span>聚焦模式</span>
          <span className="shortcut">Ctrl + .</span>
        </button>

        <div className="context-menu-separator"></div>

        <button className="context-menu-item disabled" disabled>
          导出当前主题为图片
        </button>
      </div>
    </>
  )
}
