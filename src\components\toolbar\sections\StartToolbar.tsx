import * as ToolbarPrimitive from "@radix-ui/react-toolbar"
import React from "react"
import {
  AlignCenterIcon,
  AlignLeftIcon,
  AlignRightIcon,
  BoldIcon,
  ItalicIcon,
  RedoIcon,
  SearchIcon,
  UndoIcon,
} from "../icons"
import type { ToolbarSectionProps } from "../types"
import {
  ToolbarButton,
  ToolbarColor,
  ToolbarSelect,
  ToolbarSeparator,
} from "../ui"

export const StartToolbar: React.FC<ToolbarSectionProps> = ({
  selectedNodeId,
  selectedNodeStyle,
  onAddChildNode,
  onToggleBold,
  onToggleItalic,
  onColorChange,
  onFontFamilyChange,
  onFontSizeChange,
  onTextAlignChange,
}) => {
  const isEnabled = selectedNodeId !== null

  return (
    <ToolbarPrimitive.Root className="toolbar" aria-label="开始工具栏">
      {/* 查找和替换 */}
      <ToolbarButton
        label="查找替换"
        disabled={!isEnabled}
        className="toolbar-text-btn"
      >
        <SearchIcon />
        <span>查找替换</span>
      </ToolbarButton>

      <ToolbarSeparator />

      {/* 撤销 */}
      <ToolbarButton label="撤销" disabled={!isEnabled}>
        <UndoIcon />
      </ToolbarButton>

      {/* 恢复 */}
      <ToolbarButton label="恢复" disabled={!isEnabled}>
        <RedoIcon />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* 清除格式 */}
      <ToolbarButton label="清除格式" disabled={!isEnabled}>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
        >
          <path
            d="M615.90666666 893.92c-6.2 0-13.64-1.24-19.84-4.96-3.72-1.24-8.68-2.48-12.4-4.96L164.54666666 627.32c-13.64-8.68-19.84-23.56-18.6-38.44 1.24-14.88 11.16-28.52 23.56-34.72 66.96-24.8 128.96-60.76 182.28-105.4 18.6-16.12 37.2-33.48 54.56-52.08l2.48-2.48c24.8-28.52 47.12-59.52 64.48-94.24 0-1.24 1.24-1.24 1.24-2.48 0 0 1.24-1.24 1.24-2.48s1.24-2.48 2.48-2.48c0 0 2.48-2.48 2.48-3.72 0 0 2.48-1.24 2.48-2.48 0 0 2.48-2.48 3.72-2.48l1.24-1.24s1.24-1.24 2.48-1.24c0 0 1.24-1.24 2.48-1.24 0 0 1.24 0 1.24-1.24 0 0 1.24 0 1.24-1.24 1.24 0 2.48-1.24 2.48-1.24h21.08c1.24 0 2.48 0 3.72 1.24h2.48s1.24 1.24 2.48 1.24l109.12 54.56 104.16-189.72c8.68-17.36 32.24-21.08 54.56-9.92 22.32 11.16 33.48 33.48 24.8 50.84l-104.16 189.72 96.72 48.36c6.2 3.72 11.16 7.44 14.88 13.64 4.96 6.2 7.44 14.88 7.44 23.56-1.24 76.88-18.6 151.28-50.84 221.96-34.72 74.4-81.84 140.12-141.36 197.16-7.44 11.16-17.36 14.88-27.28 14.88z"
            fill="#707070"
          />
        </svg>
      </ToolbarButton>

      <ToolbarSeparator />

      {/* 字体选择 */}
      <ToolbarSelect
        label="字体"
        options={["微软雅黑", "宋体", "Arial", "Times New Roman"]}
        value={selectedNodeStyle?.fontFamily || "微软雅黑"}
        onChange={onFontFamilyChange || (() => {})}
        disabled={!isEnabled}
        className="toolbar-font-select"
      />

      {/* 字号选择 */}
      <ToolbarSelect
        label="字号"
        options={["12", "14", "16", "18", "20", "24", "28", "32"]}
        value={(selectedNodeStyle?.fontSize?.toString() as string) || "14"}
        onChange={(val) =>
          onFontSizeChange && onFontSizeChange(parseInt(val, 10))
        }
        disabled={!isEnabled}
        className="toolbar-size-select"
      />

      <ToolbarSeparator />

      {/* 加粗 */}
      <ToolbarButton
        label="加粗"
        onClick={onToggleBold}
        disabled={!isEnabled}
        active={selectedNodeStyle?.fontWeight === "bold"}
      >
        <BoldIcon />
      </ToolbarButton>

      {/* 斜体 */}
      <ToolbarButton
        label="斜体"
        onClick={onToggleItalic}
        disabled={!isEnabled}
        active={selectedNodeStyle?.fontStyle === "italic"}
      >
        <ItalicIcon />
      </ToolbarButton>

      {/* 颜色选择 */}
      <ToolbarColor
        label="字体颜色"
        value={selectedNodeStyle?.color || "#000000"}
        onChange={onColorChange || (() => {})}
        disabled={!isEnabled}
      />

      <ToolbarSeparator />

      {/* 左对齐 */}
      <ToolbarButton
        label="左对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("left")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "left"}
      >
        <AlignLeftIcon />
      </ToolbarButton>

      {/* 居中对齐 */}
      <ToolbarButton
        label="居中对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("center")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "center"}
      >
        <AlignCenterIcon />
      </ToolbarButton>

      {/* 右对齐 */}
      <ToolbarButton
        label="右对齐"
        onClick={() => onTextAlignChange && onTextAlignChange("right")}
        disabled={!isEnabled}
        active={selectedNodeStyle?.textAlign === "right"}
      >
        <AlignRightIcon />
      </ToolbarButton>

      <ToolbarSeparator />

      {/* 子主题 */}
      <ToolbarButton
        label="子主题"
        onClick={onAddChildNode}
        disabled={!isEnabled}
      >
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
        >
          <path
            d="M128 160A96 96 0 0 1 224 64h576A96 96 0 0 1 896 160v256A96 96 0 0 1 800 512h-576A96 96 0 0 1 128 416v-256z m672 0h-576v256h576v-256z"
            fill="#707070"
          />
          <path d="M464 672v-192h96v192h-96z" fill="#707070" />
          <path
            d="M192 736A96 96 0 0 1 288 640h448a96 96 0 0 1 96 96v128a96 96 0 0 1-96 96h-448A96 96 0 0 1 192 864v-128z m544 0h-448v128h448v-128z"
            fill="#707070"
          />
        </svg>
      </ToolbarButton>

      {/* 同级主题 */}
      <ToolbarButton label="同级主题" disabled={!isEnabled}>
        <svg
          className="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
        >
          <path
            d="M96 48a48 48 0 0 1 48 48v576c0 26.496 21.504 48 48 48h352a48 48 0 0 1 0 96H192A144 144 0 0 1 48 672v-576A48 48 0 0 1 96 48z"
            fill="#707070"
          />
          <path
            d="M80 256A48 48 0 0 1 128 208h384a48 48 0 0 1 0 96H128A48 48 0 0 1 80 256z"
            fill="#707070"
          />
          <path
            d="M512 160A96 96 0 0 1 608 64h320A96 96 0 0 1 1024 160v192A96 96 0 0 1 928 448h-320A96 96 0 0 1 512 352v-192z m416 0h-320v192h320v-192z"
            fill="#707070"
          />
          <path
            d="M512 672A96 96 0 0 1 608 576h320a96 96 0 0 1 96 96v192a96 96 0 0 1-96 96h-320A96 96 0 0 1 512 864v-192z m416 0h-320v192h320v-192z"
            fill="#707070"
          />
        </svg>
      </ToolbarButton>

      {/* 其他按钮可以根据需要继续添加 */}
    </ToolbarPrimitive.Root>
  )
}
