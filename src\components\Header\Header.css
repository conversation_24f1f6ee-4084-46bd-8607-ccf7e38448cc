/* Header 容器样式 */
.header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  height: 48px;
  max-width: 100%;
}

/* 文件控制区域 */
.file-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin: 0 8px;
  white-space: nowrap;
}

/* 标签导航 */
.tab-navigation {
  display: flex;
  align-items: center;
  gap: 0;
  flex: 1;
  justify-content: center;
}

.tab-btn {
  padding: 10px 16px;
  font-size: 14px;
  color: #374151;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  position: relative;
  white-space: nowrap;
}

.tab-btn:hover {
  background-color: #f3f4f6;
}

.tab-btn.active {
  color: #14b8a6;
  font-weight: 500;
  position: relative;
}

.tab-btn.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #14b8a6;
}

/* 用户控制区域 */
.user-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.share-btn {
  padding: 6px 12px;
  background-color: #14b8a6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.15s ease;
  white-space: nowrap;
}

.share-btn:hover {
  background-color: #0d9488;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f3f4f6;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.user-avatar:hover {
  background-color: #e5e7eb;
}

/* 图标按钮 */
.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
}

.icon-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.icon-btn-sm {
  width: 28px;
  height: 28px;
}

.icon-btn-lg {
  width: 36px;
  height: 36px;
}

/* Popover 样式 */
.popover-content {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 260px;
  z-index: 1000;
}

.card-panel {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-top,
.card-bottom {
  background: white;
  border-radius: 6px;
  padding: 12px;
}

.card-top-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.file-icon {
  font-size: 20px;
  color: #a78bfa;
}

.file-info {
  flex: 1;
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
  transition: background-color 0.15s ease;
}

.action-btn:hover {
  background-color: #f3f4f6;
}

.action-icon {
  width: 12px;
  height: 12px;
}

.separator {
  width: 1px;
  height: 16px;
  background-color: #d1d5db;
  margin: 0 4px;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.folder-icon {
  font-size: 16px;
  color: #fbbf24;
}

.location-path {
  font-size: 12px;
  color: #1f2937;
  flex: 1;
}

.location-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.location-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #6b7280;
  transition: background-color 0.15s ease;
}

.location-btn:hover {
  background-color: #f3f4f6;
}

.location-icon {
  width: 12px;
  height: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 8px;
  }

  .file-controls {
    gap: 4px;
  }

  .file-name {
    display: none;
  }

  .tab-navigation {
    gap: 0;
  }

  .tab-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .user-controls {
    gap: 4px;
  }

  .share-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}
