# Redux 状态管理实现文档

## 概述

本项目使用 Redux Toolkit 实现全局状态管理，将思维导图应用的核心状态从本地组件状态迁移到全局 Redux store 中，提供更好的状态管理和调试体验。

## 技术栈

- **Redux Toolkit** - 现代 Redux 开发工具包
- **React Redux** - React 与 Redux 的官方绑定库
- **TypeScript** - 完整的类型安全支持

## 状态架构设计

### 全局状态结构

```typescript
interface RootState {
  mindMap: MindMapState    // 思维导图核心数据
  ui: UIState             // UI 界面状态
  editor: EditorState     // 编辑器状态
}
```

### 状态模块划分

#### 1. MindMap 模块 (`mindMapSlice.ts`)
管理思维导图的核心数据和操作：

```typescript
interface MindMapState {
  nodes: Record<string, MindMapNode>  // 所有节点数据
  selectedNodeId: string | null       // 当前选中的节点ID
}
```

**主要 Actions:**
- `addChildNode` - 添加子节点
- `addSiblingNode` - 添加同级节点
- `addParentNode` - 添加父节点
- `deleteNode` - 删除节点
- `deleteNodeWithChildren` - 删除节点及其子节点
- `updateNode` - 更新节点内容和样式
- `setSelectedNode` - 设置选中节点
- `resetMindMap` - 重置思维导图
- `importMindMap` - 导入思维导图

#### 2. UI 模块 (`uiSlice.ts`)
管理界面相关的状态：

```typescript
interface UIState {
  activeTab: string           // 当前激活的标签页
  showAddButton: string | null // 显示添加按钮的节点ID
}
```

**主要 Actions:**
- `setActiveTab` - 设置激活标签页
- `setShowAddButton` - 设置显示添加按钮

#### 3. Editor 模块 (`editorSlice.ts`)
管理节点编辑相关的状态：

```typescript
interface EditorState {
  isEditingNode: string | null    // 当前编辑的节点ID
  editingText: string            // 编辑中的文本
  showStylePanel: string | null  // 显示样式面板的节点ID
  editingStyle: NodeStyle        // 编辑中的样式
}
```

**主要 Actions:**
- `startEditNode` - 开始编辑节点
- `setEditingText` - 设置编辑文本
- `setEditingStyle` - 设置编辑样式
- `setShowStylePanel` - 设置样式面板显示
- `saveNodeEdit` - 保存节点编辑
- `cancelNodeEdit` - 取消节点编辑

## 文件结构

```
src/
├── store/
│   ├── index.ts           # Store 配置和类型导出
│   ├── hooks.ts           # 类型化的 Redux hooks
│   ├── mindMapSlice.ts    # 思维导图状态切片
│   ├── uiSlice.ts         # UI 状态切片
│   └── editorSlice.ts     # 编辑器状态切片
├── hooks/
│   ├── useMindMap.ts      # 思维导图操作 hook
│   ├── useNodeEditor.ts   # 节点编辑 hook
│   └── useUI.ts           # UI 状态 hook
└── types/
    └── store.ts           # Redux 相关类型定义
```

## 使用方式

### 1. 在组件中使用状态

```typescript
import { useAppSelector, useAppDispatch } from '../store/hooks'
import { setSelectedNode, addChildNode } from '../store'

function MyComponent() {
  const dispatch = useAppDispatch()
  const selectedNodeId = useAppSelector(state => state.mindMap.selectedNodeId)
  const nodes = useAppSelector(state => state.mindMap.nodes)
  
  const handleSelectNode = (nodeId: string) => {
    dispatch(setSelectedNode(nodeId))
  }
  
  const handleAddChild = (parentId: string) => {
    dispatch(addChildNode(parentId))
  }
  
  return (
    // JSX...
  )
}
```

### 2. 使用封装的 Hooks

```typescript
import { useMindMap, useNodeEditor, useUI } from '../hooks'

function MindMapPage() {
  // 思维导图操作
  const {
    mindMapNodes,
    selectedNodeId,
    setSelectedNodeId,
    addChildNode,
    updateNode
  } = useMindMap()
  
  // 节点编辑
  const {
    isEditingNode,
    editingText,
    startEditNode,
    saveNodeEdit
  } = useNodeEditor()
  
  // UI 状态
  const {
    activeTab,
    setActiveTab,
    showAddButton,
    setShowAddButton
  } = useUI()
  
  return (
    // JSX...
  )
}
```

## 核心特性

### 1. 自动持久化
思维导图数据会自动保存到 localStorage：

```typescript
// 在 mindMapSlice 中自动触发
saveMindMapToStorage(state.nodes)
```

### 2. 类型安全
完整的 TypeScript 类型支持：

```typescript
// 类型化的 hooks
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
```

### 3. 位置自动计算
添加节点时自动重新计算所有节点位置：

```typescript
// 重新计算所有节点位置
state.nodes = recalculateAllPositions(state.nodes)
```

### 4. 开发工具支持
支持 Redux DevTools 进行状态调试和时间旅行。

## 迁移说明

### 从本地状态到 Redux

**之前 (本地状态):**
```typescript
const [mindMapNodes, setMindMapNodes] = useState(initialNodes)
const [selectedNodeId, setSelectedNodeId] = useState(null)
```

**现在 (Redux):**
```typescript
const mindMapNodes = useAppSelector(state => state.mindMap.nodes)
const selectedNodeId = useAppSelector(state => state.mindMap.selectedNodeId)
const dispatch = useAppDispatch()
```

### Hook API 兼容性

为了保持向后兼容，封装的 hooks 保持了原有的 API 接口：

```typescript
// API 保持不变
const { mindMapNodes, selectedNodeId, addChildNode } = useMindMap()
```

## 最佳实践

### 1. 状态归一化
将复杂的嵌套状态扁平化存储：

```typescript
// 好的做法
nodes: Record<string, MindMapNode>

// 避免深层嵌套
nodes: {
  root: {
    children: [
      { children: [...] }
    ]
  }
}
```

### 2. 不可变更新
使用 Immer (Redux Toolkit 内置) 进行不可变更新：

```typescript
// Redux Toolkit 自动处理不可变性
state.nodes[nodeId].text = newText
```

### 3. 选择器优化
使用 useAppSelector 进行精确的状态选择：

```typescript
// 只选择需要的状态
const selectedNode = useAppSelector(state => 
  state.mindMap.selectedNodeId 
    ? state.mindMap.nodes[state.mindMap.selectedNodeId] 
    : null
)
```

## 调试和监控

### Redux DevTools
1. 安装 Redux DevTools 浏览器扩展
2. 在开发环境中自动启用
3. 可以查看所有 action 和状态变化
4. 支持时间旅行调试

### 日志记录
在开发环境中启用详细的 action 日志：

```typescript
// 在 store 配置中
middleware: (getDefaultMiddleware) =>
  getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
    },
  })
```

## 性能优化

### 1. 选择器缓存
使用 reselect 库创建记忆化选择器（如需要）。

### 2. 组件优化
使用 React.memo 和 useCallback 优化组件渲染。

### 3. 状态分片
将大型状态分解为多个小的 slice，减少不必要的重新渲染。

## 扩展指南

### 添加新的状态模块

1. 创建新的 slice 文件
2. 定义状态接口和初始状态
3. 实现 reducers 和 actions
4. 在 store 中注册新的 reducer
5. 创建对应的 hook 封装

### 添加中间件

```typescript
// 在 store/index.ts 中
import customMiddleware from './middleware/customMiddleware'

export const store = configureStore({
  reducer: { /* ... */ },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(customMiddleware),
})
```

## 总结

通过 Redux 状态管理的实现，项目获得了：

- ✅ 集中化的状态管理
- ✅ 可预测的状态更新
- ✅ 强大的调试工具支持
- ✅ 完整的类型安全
- ✅ 良好的可维护性和扩展性

这为思维导图应用提供了坚实的状态管理基础，支持未来功能的扩展和维护。
