import type { MindMapNode } from '../types/mindmap'

// localStorage 键名
export const MINDMAP_STORAGE_KEY = "mindmap-nodes"

// localStorage 工具函数
export const saveMindMapToStorage = (nodes: Record<string, MindMapNode>) => {
  try {
    localStorage.setItem(MINDMAP_STORAGE_KEY, JSON.stringify(nodes))
  } catch (error) {
    console.error("保存思维导图到本地存储失败:", error)
  }
}

export const loadMindMapFromStorage = (): Record<string, MindMapNode> | null => {
  try {
    const stored = localStorage.getItem(MINDMAP_STORAGE_KEY)
    if (stored) {
      return JSON.parse(stored)
    }
  } catch (error) {
    console.error("从本地存储加载思维导图失败:", error)
  }
  return null
}

export const clearMindMapStorage = () => {
  try {
    localStorage.removeItem(MINDMAP_STORAGE_KEY)
  } catch (error) {
    console.error("清除本地存储失败:", error)
  }
}
